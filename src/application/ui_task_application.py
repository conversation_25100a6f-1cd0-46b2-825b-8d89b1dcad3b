#!/usr/bin/env python3
"""
执行服务 - 应用层服务
使用原有的step_executor.py和test_case_parser.py，保持原有代码和prompt不变
"""
import traceback
from typing import Dict, Any, TYPE_CHECKING

from loguru import logger

from src.domain.ui_task.mobile.android.screenshot_manager import screenshot_manager
from src.domain.ui_task.mobile.repo.do.State import DeploymentState
from src.domain.ui_task.mobile.repo.do.task_stop_manager import task_stop_manager
from src.domain.ui_task.mobile.service.keyboard_service import keyboard_service
from src.domain.ui_task.mobile.service.step_executor_service import execute_current_step_node
from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service
from src.domain.ui_task.mobile.service.test_case_parser_service import parse_test_case_node
from src.domain.ui_task.mobile.service.video_service import video_service
from src.domain.ui_task.mobile.service.execution_log_service import ExecutionLogService
from src.domain.ui_task.mobile.utils.exception_handler import TaskExceptionHandler

if TYPE_CHECKING:
    from src.api.v1.dto import UITaskCreateRequest
from src.schema.action_types import ExecutionStatus

from src.domain.ui_task.mobile.aggregate import AgentAggregate
from datetime import datetime
from langgraph.graph import StateGraph, END


class UITaskApplication:
    """执行服务 - 使用原有的step_executor.py逻辑，不修改原有代码和prompt"""

    def execute_test_case_enhanced(
            self,
            task_id: str,
            request: "UITaskCreateRequest"
    ) -> Dict[str, Any]:
        """
        增强的测试用例执行方法，支持分步验证和非分步验证模式

        Args:
            task_id: 任务ID
            request: 测试用例执行请求

        Returns:
            执行结果
        """
        try:
            logger.info(f"[task_id: {task_id}] 📱 App ID: {request.app_id}")

            # 解析设备配置
            device_id = request.device.android.url

            # 预先检查设备连接状态
            logger.info(f"[task_id: {task_id}] 🔍 Checking device connection before starting execution...")
            agent_aggregate = AgentAggregate()
            device_connected = agent_aggregate.check_device_connection(device_id, task_id)
            if not device_connected:
                logger.info(f"[task_id: {task_id}] ❌ Device {device_id} is not connected, aborting test case execution")
                return {
                    "task_name": request.task_name,
                    "device": device_id,
                    "execution_status": "failed",
                    "error_message": f"Device {device_id} is not connected or not accessible",
                    "success": False,
                    "timestamp": datetime.now().isoformat()
                }

            # 初始化增强状态
            state = UITaskApplication._initialize_enhanced_state(task_id, request, device_id)

            # 创建执行图
            def route_next_action(state: DeploymentState) -> str:
                if state.get("completed", False):
                    return "end"

                # 检查分步验证模式下的失败情况
                if state.get("verification_mode") == "step_by_step":
                    current_step_index = state.get("current_step_index", 0)
                    step_retry_counts = state.get("step_retry_counts", [])
                    max_step_retries = state.get("max_step_retries", 3)

                    if (current_step_index < len(step_retry_counts) and
                            step_retry_counts[current_step_index] >= max_step_retries):
                        logger.info(
                            f"[task_id: {state['task_id']}] ⏭️ Step {current_step_index + 1} failed after {max_step_retries} retries")
                        state["completed"] = True
                        state["execution_status"] = ExecutionStatus.FAILED.value
                        state[
                            "error_message"] = f"Step {current_step_index + 1} failed verification after {max_step_retries} attempts"
                        return "end"

                # 原有的失败检查逻辑
                if state.get("step_failed", False):
                    retry_count = state.get("retry_count", 0)
                    max_retries = state.get("max_retries", 3)
                    if retry_count >= max_retries:
                        logger.info(
                            f"[task_id: {state['task_id']}] ⏭️ Test case failed after {max_retries} retries, ending execution...")
                        state["completed"] = True
                        state["execution_status"] = ExecutionStatus.FAILED.value
                        return "end"

                return "execute_step"

            # 创建图
            graph = StateGraph(DeploymentState)

            # 两种模式都需要解析步骤，只是解析策略不同
            # 分步模式：使用预定义步骤
            # 聚合模式：使用GPT-4智能拆分步骤
            graph.add_node("parse_test_case", parse_test_case_node)
            graph.add_node("execute_step", execute_current_step_node)
            graph.set_entry_point("parse_test_case")
            graph.add_edge("parse_test_case", "execute_step")

            graph.add_conditional_edges(
                "execute_step",
                route_next_action,
                {
                    "execute_step": "execute_step",
                    "end": END
                }
            )

            compiled_graph = graph.compile()

            # 在执行前更新任务状态为执行中
            task_persistence_service.update_task_status(
                task_id=task_id,
                status=ExecutionStatus.PROCESSING
            )

            # 执行图
            final_state = compiled_graph.invoke(state, {"recursion_limit": 200})

            # 生成增强结果
            result = UITaskApplication._generate_enhanced_result(final_state, request)

            # 检查任务是否被停止或已经失败，如果是则不添加完成日志
            execution_status = final_state.get("execution_status", "")
            if execution_status not in [ExecutionStatus.TERMINATE.value, ExecutionStatus.FAILED.value]:
                # 只有在任务正常完成时才添加完成日志
                success = result.get("success", False)
                completion_log = ExecutionLogService.create_task_completion_log(success)
                task_persistence_service.append_execution_log_entries(task_id, [completion_log])

            return result

        except Exception as e:
            logger.error(f"[task_id: {task_id}] ❌ Enhanced test case execution failed: {str(e)}")
            traceback.print_exc()

            # 使用统一的异常处理方法
            TaskExceptionHandler.update_task_status_to_failed(
                task_id,
                f"Enhanced test case execution failed: {str(e)}"
            )
            TaskExceptionHandler.log_execution_failure(task_id, str(e))

            return {
                "success": False,
                "error": str(e),
                "task_name": request.task_name,
                "execution_status": "failed",
                "timestamp": datetime.now().isoformat()
            }

    @staticmethod
    def _initialize_enhanced_state(
            task_id: str,
            request: "UITaskCreateRequest",
            device_id: str
    ) -> DeploymentState:
        """
        初始化增强状态

        Args:
            task_id: 任务ID
            request: 测试用例请求
            device_id: 设备ID

        Returns:
            初始化的状态
        """
        from datetime import datetime

        state = DeploymentState()

        # 基本信息
        state["task_id"] = task_id
        state["test_case_name"] = request.task_name
        state["app_package"] = request.app_id
        state["device"] = device_id
        state["device_type"] = request.device.type
        state["device_config"] = request.device.model_dump()
        state["agent_config_id"] = request.agent_config_id

        # 验证模式和期望结果
        state["verification_mode"] = request.execution_mode

        if request.task_step_by_step:
            state["step_expected_results"] = [
                {
                    "text": step.expect_result.text if step.expect_result else None,
                    "image": step.expect_result.image if step.expect_result else None,
                    "wait_time": (step.expect_result.wait_time if step.expect_result and step.expect_result.wait_time is not None else 1.5)
                } for step in request.task_step_by_step
            ]

            # 提取每个步骤的等待时间
            step_wait_times = []
            for step in request.task_step_by_step:
                wait_time = 1.5  # 默认等待时间
                if step.expect_result and step.expect_result.wait_time is not None:
                    wait_time = step.expect_result.wait_time
                step_wait_times.append(wait_time)
            state["step_wait_times"] = step_wait_times

            state['task_steps'] = []
            for step in request.task_step_by_step:
                state['task_steps'].append(step.step)
            state["test_case_description"] = "\n".join(
                [f"{step.step}" for _, step in enumerate(request.task_step_by_step)])
        else:
            # 如果没有提供步骤，创建空的结构
            state["step_expected_results"] = []
            state["task_steps"] = []
            state["test_case_description"] = ""

        state["overall_expected_result"] = {
            "text": request.task_expect_result.text if request.task_expect_result else "",
            "image": request.task_expect_result.image if request.task_expect_result else ""
        }
        state["expected_result"] = request.task_expect_result.text if request.task_expect_result else ""

        # Prompt参数化字段
        state["app_name"] = request.app_name
        state["app_description"] = request.app_description
        state["ui_component_instructions"] = request.ui_component_instructions
        state["special_scenarios"] = request.special_scenarios

        # 应用重启控制字段
        state["is_restart"] = request.is_restart

        # 应用前台检查控制字段
        state["app_foreground_check"] = request.app_foreground_check

        # 验证相关状态
        state["step_verification_results"] = []
        state["step_retry_counts"] = [0] * len(state["task_steps"])
        state["max_step_retries"] = 3
        state["current_step_index"] = 0

        # 执行状态
        state["completed"] = False
        state["execution_status"] = ExecutionStatus.PROCESSING.value
        state["step_failed"] = False
        state["retry_count"] = 0
        state["max_retries"] = 3
        state["error_message"] = None
        
        # 验证失败恢复相关字段
        state["verification_failure_reason"] = None
        state["execution_blocked"] = False
        state["block_reason"] = None

        # 历史和上下文
        state["history"] = []
        state["current_page"] = {}

        # 添加初始化记录
        state["history"].append({
            "action": "enhanced_initialization",
            "task_name": request.task_name,
            "verification_mode": state["verification_mode"],
            "device_type": request.device.type,
            "agent_config_id": request.agent_config_id,
            "timestamp": datetime.now().isoformat()
        })

        return state

    @staticmethod
    def _generate_enhanced_result(
            final_state: DeploymentState,
            request: "UITaskCreateRequest"
    ) -> Dict[str, Any]:
        """
        生成增强的执行结果

        Args:
            final_state: 最终状态
            request: 原始请求

        Returns:
            执行结果
        """
        from datetime import datetime

        verification_mode = final_state.get("verification_mode", "aggregation")

        # 安全地获取执行状态的字符串值
        execution_status = final_state.get("execution_status", "unknown")
        if hasattr(execution_status, 'value'):
            execution_status_str = execution_status.value
        else:
            execution_status_str = str(execution_status)

        result = {
            "task_name": final_state.get("test_case_name", request.task_name),
            "verification_mode": verification_mode,
            "device": final_state.get("device", "unknown"),
            "device_type": final_state.get("device_type", "android"),
            "execution_status": execution_status_str,
            "timestamp": datetime.now().isoformat()
        }

        # 分步验证模式的结果
        if verification_mode == "step_by_step":
            step_verification_results = final_state.get("step_verification_results", [])
            current_step_index = final_state.get("current_step_index", 0)

            result.update({
                "current_step_index": current_step_index,
                "step_verification_results": step_verification_results
            })

        # 通用结果字段
        result.update({
            "error_message": final_state.get("error_message"),
            "overall_expected_result": final_state.get("overall_expected_result", {}),
            "success": final_state.get("execution_status") == ExecutionStatus.SUCCEED.value
        })

        return result

    @staticmethod
    def setup_adb_keyboards(task_id: str, device_id: str) -> bool:
        """
        为任务设置ADB键盘

        Args:
            task_id: 任务ID
            device_id: 设备ID

        Returns:
            是否设置成功
        """
        return keyboard_service.setup_adb_keyboards_for_task(task_id, device_id)

    @staticmethod
    def cleanup_adb_keyboards(task_id: str) -> bool:
        """
        为任务清理ADB键盘

        Args:
            task_id: 任务ID

        Returns:
            是否清理成功
        """
        return keyboard_service.cleanup_adb_keyboards_for_task(task_id)

    @staticmethod
    def get_task_by_id(task_id: str):
        """获取任务信息"""
        return task_persistence_service.get_task_by_task_id(task_id)

    @staticmethod
    def get_task_actions(task_id: str):
        """获取任务动作列表"""
        return task_persistence_service.get_task_actions(task_id)

    @staticmethod
    def stop_task(task_id: str) -> bool:
        """停止任务"""
        # 停止执行线程
        thread_stopped = task_stop_manager.stop_task(task_id)
        # 更新数据库状态
        db_updated = task_persistence_service.stop_task_execution(task_id)
        return thread_stopped and db_updated

    @staticmethod
    def delete_task(task_id: str) -> bool:
        """删除任务"""
        return task_persistence_service.delete_task_and_actions(task_id)

    @staticmethod
    def get_screenshot_full_path(relative_path: str):
        """获取截图完整路径"""
        return screenshot_manager.get_screenshot_full_path(relative_path)

    @staticmethod
    def screenshot_exists(relative_path: str) -> bool:
        """检查截图是否存在"""
        return screenshot_manager.screenshot_exists(relative_path)

    @staticmethod
    def get_task_screenshot_dir(task_id: str):
        """获取任务截图目录"""
        return screenshot_manager.get_task_screenshot_dir(task_id)

    @staticmethod
    def append_execution_log(task_id: str, log_content: str):
        """追加执行日志"""
        return task_persistence_service.append_execution_log(task_id, log_content)

    @staticmethod
    def start_task_execution(task_id: str):
        """开始任务执行"""
        return task_persistence_service.start_task_execution(task_id)

    @staticmethod
    def complete_task_execution(task_id: str, success: bool, error_message: str = None):
        """完成任务执行"""
        return task_persistence_service.complete_task_execution(task_id, success, error_message)

    @staticmethod
    def update_task_status(task_id: str, status, error_message: str = None):
        """更新任务状态"""
        return task_persistence_service.update_task_status(task_id, status, error_message)

    @staticmethod
    def create_task_from_request(task_id: str, request, device_id: str):
        """从请求创建任务"""
        return task_persistence_service.create_task_from_request(task_id, request, device_id)

    @staticmethod
    def register_task(task_id: str, task_name: str):
        """注册任务到停止管理器"""
        return task_stop_manager.register_task(task_id, task_name)

    @staticmethod
    def unregister_task(task_id: str):
        """从停止管理器注销任务"""
        return task_stop_manager.unregister_task(task_id)

    @staticmethod
    def is_task_stopped(task_id: str) -> bool:
        """检查任务是否被停止"""
        return task_stop_manager.is_task_stopped(task_id)

    @staticmethod
    def stop_task_thread(task_id: str) -> bool:
        """停止任务线程"""
        return task_stop_manager.stop_task(task_id)

    @staticmethod
    def stop_task_execution(task_id: str) -> bool:
        """停止任务执行并更新状态"""
        return task_persistence_service.stop_task_execution(task_id)

    @staticmethod
    def video_exists(task_id: str) -> bool:
        """
        检查任务视频是否存在

        Args:
            task_id: 任务ID

        Returns:
            是否存在
        """
        return video_service.video_exists(task_id)

    @staticmethod
    def delete_task_video(task_id: str) -> bool:
        """
        删除任务视频文件

        Args:
            task_id: 任务ID

        Returns:
            是否删除成功
        """
        return video_service.delete_task_video(task_id)

    @staticmethod
    def start_video_stream(expected_size: int = None):
        """
        开始视频流式上传

        Args:
            expected_size: 预期文件大小

        Returns:
            (会话ID, 错误信息) 元组
        """
        return video_service.start_stream_upload(expected_size)

    @staticmethod
    async def append_video_chunk(
            video_id: str,
            chunk_data: bytes,
            chunk_index: int = None
    ):
        """
        追加视频数据块

        Args:
            video_id: 上传会话ID
            chunk_data: 数据块
            chunk_index: 数据块序号

        Returns:
            (是否成功, 错误信息) 元组
        """
        return await video_service.append_stream_chunk(video_id, chunk_data, chunk_index)

    @staticmethod
    def complete_video_stream(video_id: str):
        """
        完成视频流式上传

        Args:
            video_id: 上传会话ID

        Returns:
            (最终文件路径, 错误信息) 元组
        """
        return video_service.complete_stream_upload(video_id)

    @staticmethod
    def cancel_video_stream(video_id: str):
        """
        取消视频流式上传

        Args:
            video_id: 上传会话ID

        Returns:
            (是否成功, 错误信息) 元组
        """
        return video_service.cancel_stream_upload(video_id)

    @staticmethod
    def get_video_stream_status(video_id: str):
        """
        获取视频流式上传状态

        Args:
            video_id: 上传会话ID

        Returns:
            状态信息
        """
        return video_service.get_stream_status(video_id)

    @staticmethod
    def analyze_video_with_ai(video_id: str, verification_mode: str = "step_by_step"):
        """
        使用AI分析视频并生成测试用例步骤

        Args:
            video_id: 视频会话ID
            verification_mode: 验证模式 ("step_by_step" 或 "aggregation")

        Returns:
            (分析结果, 错误信息) 元组
        """
        return video_service.analyze_video_with_ai(video_id, verification_mode)

    @staticmethod
    def optimize_test_steps(steps: str):
        """
        使用AI优化测试用例步骤格式

        Args:
            steps: 需要优化的步骤内容

        Returns:
            (优化结果, 错误信息) 元组
        """
        from src.domain.ui_task.mobile.service.step_optimize_service import step_optimize_service
        return step_optimize_service.optimize_steps(steps)


ui_task_application = UITaskApplication()
